/**
 * WebSocket聊天客户端
 * 处理实时聊天连接和消息传输
 */

import { appConfig } from '@/config/env'

export interface ChatMessage {
  message_id: string
  content: string
  role: 'user' | 'assistant'
  message_type: 'text' | 'audio'
  timestamp: string
  audio_duration?: number
  transcription?: string
  confidence?: number
}

export interface WebSocketMessage {
  type: string
  data: any
}

export interface WebSocketConfig {
  url: string
  userId: string
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
}

export class WebSocketChatClient {
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private reconnectAttempts = 0
  private heartbeatTimer: number | null = null
  private reconnectTimer: number | null = null
  private isConnecting = false
  private isManualClose = false

  // 公开属性用于外部访问
  public userId: string
  public sessionId?: string

  // 事件回调
  private onMessageCallback?: (message: ChatMessage) => void
  private onTypingCallback?: (isTyping: boolean) => void
  private onConnectedCallback?: () => void
  private onDisconnectedCallback?: () => void
  private onErrorCallback?: (error: string) => void
  private onVoiceMessageCallback?: (data: any) => void
  private onSystemMessageCallback?: (type: string, data: any) => void

  constructor(config: WebSocketConfig) {
    this.config = {
      reconnectInterval: appConfig.websocket.reconnectInterval,
      maxReconnectAttempts: appConfig.websocket.maxReconnectAttempts,
      heartbeatInterval: appConfig.websocket.heartbeatInterval,
      ...config
    }
    this.userId = config.userId
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return
    }

    this.isConnecting = true
    this.isManualClose = false

    try {
      // 构建WebSocket URL
      const wsUrl = `${appConfig.wsBaseUrl}/api/v1/airelief/chat/ws/${this.config.userId}`

      console.log('正在连接WebSocket:', wsUrl)

      this.ws = new WebSocket(wsUrl)
      this.setupEventListeners()

      // 等待连接建立
      await new Promise<void>((resolve, reject) => {
        if (!this.ws) {
          reject(new Error('WebSocket创建失败'))
          return
        }

        const onOpen = () => {
          this.ws?.removeEventListener('open', onOpen)
          this.ws?.removeEventListener('error', onError)
          resolve()
        }

        const onError = () => {
          this.ws?.removeEventListener('open', onOpen)
          this.ws?.removeEventListener('error', onError)
          reject(new Error('WebSocket连接失败'))
        }

        this.ws.addEventListener('open', onOpen)
        this.ws.addEventListener('error', onError)
      })

    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.isConnecting = false
      throw error
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.ws) return

    this.ws.onopen = () => {
      console.log('WebSocket连接已建立')
      this.isConnecting = false
      this.reconnectAttempts = 0
      this.startHeartbeat()
      this.onConnectedCallback?.()
    }

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event.code, event.reason)
      this.cleanup()
      this.onDisconnectedCallback?.()

      // 如果不是手动关闭，尝试重连
      if (!this.isManualClose && this.reconnectAttempts < this.config.maxReconnectAttempts!) {
        this.scheduleReconnect()
      }
    }

    this.ws.onerror = (event) => {
      console.error('WebSocket错误:', event)
      this.onErrorCallback?.('连接错误')
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    console.log('收到WebSocket消息:', message)

    switch (message.type) {
      case 'ai_message':
        this.onMessageCallback?.({
          message_id: message.data.message_id,
          content: message.data.content,
          role: 'assistant',
          message_type: 'text',
          timestamp: message.data.timestamp
        })
        break

      case 'voice_message_sent':
        this.onVoiceMessageCallback?.(message.data)
        break

      case 'typing_status':
        this.onTypingCallback?.(message.data.is_typing)
        break

      case 'message_status':
        // 消息状态更新
        console.log('消息状态:', message.data)
        break

      case 'connected':
        console.log('连接确认:', message.data)
        break

      case 'error':
        console.error('服务器错误:', message.data.message)
        this.onErrorCallback?.(message.data.message)
        break

      case 'pong':
        // 心跳响应
        break

      case 'streaming_started':
        this.onSystemMessageCallback?.('streaming_started', message.data)
        break

      case 'streaming_result':
        this.onSystemMessageCallback?.('streaming_result', message.data)
        break

      case 'streaming_ended':
        this.onSystemMessageCallback?.('streaming_ended', message.data)
        break

      default:
        this.onSystemMessageCallback?.(message.type, message.data)
        break
    }
  }

  /**
   * 发送文本消息
   */
  sendTextMessage(content: string): void {
    if (!this.isConnected()) {
      this.onErrorCallback?.('连接未建立')
      return
    }

    const message = {
      type: 'text',
      content: content.trim(),
      timestamp: new Date().toISOString()
    }

    this.ws!.send(JSON.stringify(message))

    // 立即显示用户消息
    this.onMessageCallback?.({
      message_id: `temp_${Date.now()}`,
      content: content.trim(),
      role: 'user',
      message_type: 'text',
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 发送语音消息
   */
  sendVoiceMessage(audioData: string, duration: number, extraData?: any): void {
    if (!this.isConnected()) {
      this.onErrorCallback?.('连接未建立')
      return
    }

    const message = {
      type: 'audio',
      audio_data: audioData,
      duration: duration,
      timestamp: new Date().toISOString(),
      ...extraData // 合并额外的数据
    }

    this.ws!.send(JSON.stringify(message))
  }

  /**
   * 开始流式识别
   */
  startStreamingRecognition(): boolean {
    if (!this.isConnected()) {
      console.error('❌ 启动流式识别失败: WebSocket连接未建立')
      this.onErrorCallback?.('连接未建立')
      return false
    }

    const message = {
      type: 'streaming_start',
      data: {}
    }

    console.log('📤 发送流式识别启动消息:', message)
    this.ws!.send(JSON.stringify(message))
    return true
  }

  /**
   * 发送流式音频数据
   */
  sendStreamingAudio(audioData: string): boolean {
    if (!this.isConnected()) {
      return false
    }

    const message = {
      type: 'streaming_audio',
      data: {
        audio_data: audioData
      }
    }

    this.ws!.send(JSON.stringify(message))
    return true
  }

  /**
   * 结束流式识别
   */
  endStreamingRecognition(): boolean {
    if (!this.isConnected()) {
      return false
    }

    const message = {
      type: 'streaming_end',
      data: {}
    }

    this.ws!.send(JSON.stringify(message))
    return true
  }

  /**
   * 发送心跳
   */
  private sendHeartbeat(): void {
    if (this.isConnected()) {
      this.ws!.send(JSON.stringify({ type: 'ping' }))
    }
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    this.heartbeatTimer = window.setInterval(() => {
      this.sendHeartbeat()
    }, this.config.heartbeatInterval!)
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    this.reconnectAttempts++
    console.log(`准备重连 (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`)

    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch(error => {
        console.error('重连失败:', error)
      })
    }, this.config.reconnectInterval!)
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    this.stopHeartbeat()
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  /**
   * 手动断开连接
   */
  disconnect(): void {
    this.isManualClose = true
    this.cleanup()
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  /**
   * 设置事件回调
   */
  onMessage(callback: (message: ChatMessage) => void): void {
    this.onMessageCallback = callback
  }

  onTyping(callback: (isTyping: boolean) => void): void {
    this.onTypingCallback = callback
  }

  onConnected(callback: () => void): void {
    this.onConnectedCallback = callback
  }

  onDisconnected(callback: () => void): void {
    this.onDisconnectedCallback = callback
  }

  onError(callback: (error: string) => void): void {
    this.onErrorCallback = callback
  }

  onVoiceMessage(callback: (data: any) => void): void {
    this.onVoiceMessageCallback = callback
  }

  onSystemMessage(callback: (type: string, data: any) => void): void {
    this.onSystemMessageCallback = callback
  }
}
