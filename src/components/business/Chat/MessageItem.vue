<script setup lang="ts">
import { computed, ref } from 'vue'
import { showToast } from 'vant'
import type { ChatMessage } from '../../../types/api'
import { useUserStore } from '@/stores/user'
import { isWechatBrowser } from '@/utils/wechat'
import aiAvatarImg from '@/assets/images/ai-avatar.png'
import userAvatarImg from '@/assets/images/user-avtar.png'

interface Props {
  message: ChatMessage
}

const props = defineProps<Props>()
const userStore = useUserStore()

const isMessageFromAI = computed(() => props.message.role === 'assistant')
const isAudio = computed(() => props.message.type === 'audio')
const isDivider = computed(() => props.message.type === 'divider')
const isPlaying = ref(false)

// 处理消息内容中的换行符
const formattedContent = computed(() => {
  if (!props.message.content) return ''

  // 将 \n 转换为实际的换行符，并处理其他可能的换行符格式
  return props.message.content
    .replace(/\\n/g, '\n')  // 处理转义的换行符
    .replace(/\r\n/g, '\n') // 处理Windows换行符
    .replace(/\r/g, '\n')   // 处理Mac换行符
    .trim()
})

// 获取用户头像，优先使用store中的头像，否则使用默认头像
const userAvatar = computed(() => {
  return userStore.userInfo?.avatar || userAvatarImg
})

// 检查浏览器音频格式支持
const checkAudioSupport = (mimeType: string): boolean => {
  const audio = document.createElement('audio')
  return audio.canPlayType(mimeType) !== ''
}

// 播放语音消息
const playAudio = () => {
  if (!isAudio.value) return

  // 调试信息收集
  const debugInfo = {
    audioUrl: props.message.audioUrl ? '有服务器URL' : '无服务器URL',
    localId: props.message.localId ? '有微信localId' : '无微信localId',
    localAudioData: props.message.localAudioData ? '有本地数据' : '无本地数据',
    localAudioBlob: props.message.localAudioBlob ? '有本地Blob' : '无本地Blob',
    transcription: props.message.transcription || '无转录文本',
    isWechat: isWechatBrowser() ? '微信环境' : '非微信环境',
    isLocalOnly: props.message.isLocalOnly ? '仅本地' : '已上传'
  }

  // 检查浏览器音频格式支持
  const supportInfo = {
    webm: checkAudioSupport('audio/webm') ? '支持' : '不支持',
    wav: checkAudioSupport('audio/wav') ? '支持' : '不支持',
    mp3: checkAudioSupport('audio/mp3') ? '支持' : '不支持',
    ogg: checkAudioSupport('audio/ogg') ? '支持' : '不支持'
  }

  // 在toast中显示调试信息
  const debugText = Object.entries(debugInfo).map(([key, value]) => `${key}:${value}`).join(', ')
  const supportText = Object.entries(supportInfo).map(([key, value]) => `${key}:${value}`).join(', ')
  console.log('🎵 [MessageItem] 播放调试信息:', debugText)
  console.log('🎵 [MessageItem] 格式支持信息:', supportText)

  // 显示调试信息给用户
  showToast(`调试: ${debugText}`)
  setTimeout(() => showToast(`格式支持: ${supportText}`), 1000)

  // 优先级：本地音频 > 微信localId > 服务器audioUrl

  // 1. 如果有本地音频Blob，优先使用（最佳体验）
  if (props.message.localAudioBlob) {
    try {
      showToast('尝试播放本地Blob音频')
      isPlaying.value = true
      const audioUrl = URL.createObjectURL(props.message.localAudioBlob)
      const audio = new Audio(audioUrl)

      audio.onloadstart = () => {
        showToast('本地音频开始加载')
      }

      audio.oncanplay = () => {
        showToast('本地音频可以播放')
      }

      audio.onplay = () => {
        showToast('本地音频开始播放')
      }

      audio.onended = () => {
        showToast('本地音频播放完成')
        isPlaying.value = false
        URL.revokeObjectURL(audioUrl) // 清理URL对象
      }

      audio.onerror = (error) => {
        const errorMsg = `本地音频播放错误: ${(error as any)?.type || 'unknown'}`
        showToast(errorMsg)
        isPlaying.value = false
        URL.revokeObjectURL(audioUrl) // 清理URL对象
      }

      audio.play().then(() => {
        showToast('本地音频播放启动成功')
      }).catch((error: any) => {
        const errorMsg = `本地音频播放启动失败: ${error?.message || error?.name || 'unknown'}`
        showToast(errorMsg)
        isPlaying.value = false
        URL.revokeObjectURL(audioUrl) // 清理URL对象
      })
    } catch (error: any) {
      const errorMsg = `本地Blob播放异常: ${error?.message || 'unknown'}`
      showToast(errorMsg)
      isPlaying.value = false
    }
  }
  // 2. 如果有本地音频数据（base64），使用data URL播放
  else if (props.message.localAudioData) {
    try {
      showToast('尝试播放本地音频数据')
      isPlaying.value = true
      const audio = new Audio(props.message.localAudioData)

      audio.onended = () => {
        showToast('本地音频数据播放完成')
        isPlaying.value = false
      }

      audio.onerror = (error) => {
        const errorMsg = `本地音频数据播放错误: ${(error as any)?.type || 'unknown'}`
        showToast(errorMsg)
        isPlaying.value = false
      }

      audio.play().then(() => {
        showToast('本地音频数据播放启动成功')
      }).catch((error: any) => {
        const errorMsg = `本地音频数据播放启动失败: ${error?.message || error?.name || 'unknown'}`
        showToast(errorMsg)
        isPlaying.value = false
      })
    } catch (error: any) {
      const errorMsg = `本地音频数据播放异常: ${error?.message || 'unknown'}`
      showToast(errorMsg)
      isPlaying.value = false
    }
  }
  // 3. 如果是微信环境且有localId，使用微信播放
  else if (isWechatBrowser() && props.message.localId && window.wx) {
    try {
      showToast('尝试微信语音播放')
      if (isPlaying.value) {
        // 如果正在播放，则停止播放
        window.wx.stopVoice({
          localId: props.message.localId
        })
        isPlaying.value = false
        showToast('停止微信语音播放')
      } else {
        // 开始播放
        window.wx.playVoice({
          localId: props.message.localId,
          success: () => {
            showToast('微信语音播放启动成功')
            isPlaying.value = true
          },
          fail: (res: any) => {
            const errorMsg = `微信语音播放失败: ${res?.errMsg || 'unknown'}`
            showToast(errorMsg)
            isPlaying.value = false
          }
        })

        // 监听播放结束
        window.wx.onVoicePlayEnd({
          complete: (res: any) => {
            if (res.localId === props.message.localId) {
              isPlaying.value = false
              showToast('微信语音播放结束')
            }
          }
        })
      }
    } catch (error: any) {
      const errorMsg = `微信语音播放异常: ${error?.message || 'unknown'}`
      showToast(errorMsg)
      isPlaying.value = false
    }
  }
  // 4. 如果有服务器audioUrl，使用HTML5 Audio播放
  else if (props.message.audioUrl) {
    try {
      // 分析音频URL类型
      const isDataUrl = props.message.audioUrl.startsWith('data:')
      const isFileUrl = props.message.audioUrl.startsWith('http')
      const urlType = isDataUrl ? 'DataURL' : (isFileUrl ? 'FileURL' : 'Unknown')

      // 获取音频格式信息
      let audioFormat = 'unknown'
      if (isDataUrl) {
        const match = props.message.audioUrl.match(/data:audio\/([^;]+)/)
        audioFormat = match ? match[1] : 'unknown'
      } else if (isFileUrl) {
        const match = props.message.audioUrl.match(/\.([^.?]+)(\?|$)/)
        audioFormat = match ? match[1] : 'unknown'
      }

      showToast(`播放${urlType}音频(${audioFormat}): ${props.message.audioUrl.substring(0, 30)}...`)

      isPlaying.value = true
      const audio = new Audio()

      // 设置详细的事件监听
      audio.onloadstart = () => {
        showToast(`${urlType}音频开始加载`)
      }

      audio.onloadedmetadata = () => {
        showToast(`${urlType}音频元数据加载完成，时长: ${audio.duration.toFixed(1)}s`)
      }

      audio.oncanplay = () => {
        showToast(`${urlType}音频可以播放`)
      }

      audio.onplay = () => {
        showToast(`${urlType}音频开始播放`)
      }

      audio.onended = () => {
        showToast(`${urlType}音频播放完成`)
        isPlaying.value = false
      }

      audio.onerror = () => {
        const audioError = audio.error
        let errorDetails = 'unknown'
        if (audioError) {
          switch (audioError.code) {
            case MediaError.MEDIA_ERR_ABORTED:
              errorDetails = 'ABORTED(用户中止)'
              break
            case MediaError.MEDIA_ERR_NETWORK:
              errorDetails = 'NETWORK(网络错误)'
              break
            case MediaError.MEDIA_ERR_DECODE:
              errorDetails = 'DECODE(解码错误)'
              break
            case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
              errorDetails = 'NOT_SUPPORTED(格式不支持)'
              break
            default:
              errorDetails = `CODE_${audioError.code}`
          }
        }
        const errorMsg = `${urlType}音频播放错误: ${errorDetails}`
        showToast(errorMsg)
        isPlaying.value = false
      }

      // 设置音频源
      audio.src = props.message.audioUrl

      // 尝试播放
      audio.play().then(() => {
        showToast(`${urlType}音频播放启动成功`)
      }).catch((error: any) => {
        const errorMsg = `${urlType}音频播放启动失败: ${error?.message || error?.name || 'unknown'}`
        showToast(errorMsg)
        isPlaying.value = false
      })
    } catch (error: any) {
      const errorMsg = `服务器音频播放异常: ${error?.message || 'unknown'}`
      showToast(errorMsg)
      isPlaying.value = false
    }
  }
  // 5. 没有可播放的音频，显示转录文本
  else {
    showToast('没有可播放的音频文件')
    if (props.message.transcription) {
      showToast(`语音内容: ${props.message.transcription}`)
    } else {
      showToast('语音文件不可用，请检查网络连接')
    }
  }
}
</script>

<template>
  <!-- 分割线样式 -->
  <div v-if="isDivider" class="message-divider"></div>

  <!-- 普通消息 -->
  <div v-else class="message-item" :class="{ 'message-item--ai': isMessageFromAI, 'message-item--user': !isMessageFromAI }">
    <div class="message-item__avatar">
      <van-image
        :src="isMessageFromAI ? aiAvatarImg : userAvatar"
        round
        width="40"
        height="40"
        fit="cover"
      />
    </div>
    <div class="message-item__content">
      <template v-if="isAudio">
        <div class="message-item__audio-wrapper">
          <div class="message-item__audio" @click="playAudio" :class="{ 'message-item__audio--playing': isPlaying }">
            <div class="message-item__audio-duration">{{ message.audioDuration }}″</div>
            <img
              src="@/assets/icon/chat-voiceMsg.svg"
              alt="语音消息"
              class="message-item__audio-icon"
              :class="{
                'message-item__audio-icon--right': !isMessageFromAI,
                'message-item__audio-icon--playing': isPlaying
              }"
            />
            <!-- 播放状态指示 -->
            <div v-if="isPlaying" class="message-item__audio-playing-indicator">
              <span class="playing-dot"></span>
              <span class="playing-dot"></span>
              <span class="playing-dot"></span>
            </div>
          </div>
          <!-- 显示语音转录文本 -->
          <div v-if="message.transcription" class="message-item__transcription">
            "{{ message.transcription }}"
          </div>
        </div>
      </template>
      <template v-else>
        <div class="message-item__text">{{ formattedContent }}</div>
      </template>
    </div>
  </div>
</template>

<style>
/* 分割线样式 */
.message-divider {
  position: relative;
  width: 90%;
  height: 0px;
  margin: 20px auto;
  border: 0.5px solid #e1e1e1;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
  padding: 0 19px;
}

.message-item--ai {
  flex-direction: row;
}

.message-item--ai .message-item__content {
  background-color: #FFFFFF;
  border-radius: 0px 8px 8px 8px;
  margin-left: 8px;
  color: #222222;
}

.message-item--user {
  flex-direction: row-reverse;
}

.message-item--user .message-item__content {
  background-color: #5F59FF;
  border-radius: 12px 0px 12px 12px;
  margin-right: 8px;
  color: #FFFFFF;
}

.message-item__avatar {
  flex-shrink: 0;
}

/* 气泡长度，间距 */
.message-item__content {
  padding: 8px 12px;
  max-width: 75%;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  font-weight: 300;
  display: flex;
  align-items: center;
  min-height: 24px;
}

.message-item__text {
  word-break: break-word;
  line-height: 1.5;
  white-space: pre-line;
}

.message-item__audio-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.message-item__audio {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 72px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.message-item__audio:hover {
  opacity: 0.8;
}

.message-item__audio:active {
  opacity: 0.6;
}

.message-item__audio-icon {
  width: 18px;
  height: 20px;
}

.message-item__audio-icon--right {
  transform: scaleX(-1);
}

.message-item__audio-duration {
  font-size: 16px;
  line-height: 1.5;
}

.message-item--user .message-item__audio-duration {
  color: #FFFFFF;
}

.message-item--ai .message-item__audio-duration {
  color: #666;
}

.message-item__transcription {
  font-size: 12px;
  line-height: 1.4;
  opacity: 0.8;
  font-style: italic;
  margin-top: 2px;
  word-break: break-word;
}

.message-item--user .message-item__transcription {
  color: rgba(255, 255, 255, 0.8);
}

.message-item--ai .message-item__transcription {
  color: rgba(34, 34, 34, 0.6);
}

/* 播放状态样式 */
.message-item__audio--playing {
  background-color: rgba(95, 89, 255, 0.1);
  border-radius: 4px;
}

.message-item__audio-icon--playing {
  opacity: 0.8;
}

.message-item__audio-playing-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-left: 4px;
}

.playing-dot {
  width: 3px;
  height: 3px;
  background-color: #5F59FF;
  border-radius: 50%;
  animation: playing-pulse 1.4s infinite ease-in-out;
}

.playing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.playing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.playing-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes playing-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}
</style>