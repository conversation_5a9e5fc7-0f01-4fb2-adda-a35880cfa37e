// 用户相关类型
export interface UserInfo {
  id: number; // 数据库ID
  user_id: string; // 用户唯一标识ID
  wechat_openid: string; // 微信OpenID
  wechat_unionid?: string; // 微信UnionID
  
  // 微信授权相关字段
  wechat_access_token?: string; // 微信访问令牌
  wechat_refresh_token?: string; // 微信刷新令牌
  wechat_token_expires_in?: number; // access_token有效期(秒)
  wechat_token_expires_at?: string; // 令牌过期时间
  
  nickname: string;
  avatar?: string;
  phone?: string;
  gender: number; // 性别：0=未知, 1=女性, 2=男性
  birthday?: string;

  // 微信地理信息
  wechat_province?: string;
  wechat_city?: string;
  wechat_country?: string;
  wechat_privilege: string[];
  is_wechat_subscribed: boolean;
  wechat_subscribe_time?: string;
  wechat_unsubscribe_time?: string;

  // AI-Relief 特有字段
  isNewUser: boolean;
  duration: number; // 剩余时长(秒)
  totalDuration: number; // 总购买时长(秒)
  consumedDuration: number; // 已陪伴时长(秒)
  companionDays: number; // 已陪伴天数
  expiryDate?: string; // 会员到期时间
  ipLocation?: string; // IP属地

  // 状态字段
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

// 登录相关类型
export interface LoginResponse {
  token: string;
  userInfo: UserInfo;
}

// 协议相关类型
export interface AgreementStatus {
  serviceAgreement: boolean;
  privacyPolicy: boolean;
  version: string;
}

// 聊天相关类型
export interface ChatMessage {
  id: string;
  content: string;
  type: 'text' | 'audio' | 'divider'; // 添加分割线类型
  role: 'user' | 'assistant' | 'system'; // 添加系统角色
  timestamp: number;
  audioDuration?: number; // 语音消息时长(秒)
  audioUrl?: string; // 语音消息URL（服务器端）
  transcription?: string; // 语音识别文本
  localId?: string; // 微信本地媒体ID（用于播放）
  sessionId?: string; // 会话ID
  sessionEnded?: boolean; // 会话是否已结束

  // 本地音频缓存相关字段
  localAudioData?: string; // 本地音频数据（base64）
  localAudioBlob?: Blob; // 本地音频Blob对象
  isLocalOnly?: boolean; // 是否仅为本地消息（尚未上传到服务器）
  serverMessageId?: string; // 服务器端消息ID（用于更新本地消息）
}

export interface OnboardingMessage {
  id: string;
  content: string;
  type: 'text';
  delay: number; // 延迟多少毫秒后显示
}

// 支付相关类型 - 更新以匹配后端模型
export interface PaymentPackage {
  id: number; 
  name: string;
  duration: string; // 例如: "30天"
  duration_seconds: number; // 时长秒数
  price: number;
  original_price?: number; 
  tag?: string; // 例如: "推荐", "热门"
  is_active: boolean; // 是否激活
  sort_order: number; // 排序
  created_at?: string; // 创建时间
  updated_at?: string; // 更新时间
}

// 微信支付JSAPI参数
export interface WeChatPayParams {
  appId: string;
  timeStamp: string;
  nonceStr: string;
  package: string; // prepay_id=xxx
  signType: string; // 'RSA'
  paySign: string;
}

// 创建订单请求
export interface CreateOrderRequest {
  user_id: string;
  package_id: number;
  coupon_code?: string;
}

// 创建订单响应
export interface CreateOrderResponse {
  order_id: string;
  pay_params: WeChatPayParams;
}

// 订单信息
export interface OrderInfo {
  id: number;
  order_id: string;
  user_info: {
    user_id: string;
    nickname: string;
    phone?: string;
    wechat_openid: string;
  };
  package_name: string;
  amount: number;
  original_amount: number;
  discount_amount: number;
  coupon_code?: string;
  status: OrderStatus;
  payment_method: string;
  wechat_transaction_id?: string;
  wechat_prepay_id?: string;
  wechat_bank_type?: string;
  created_at: string;
  pay_time?: string;
  updated_at: string;
  // 套餐信息
  package_duration?: string; // 套餐时长描述，如 "30天"
  package_duration_seconds?: number; // 套餐时长秒数
}

// 订单状态
export type OrderStatus = 'pending' | 'paid' | 'failed' | 'cancelled' | 'refunded' | 'expired';

// 订单状态查询响应
export interface OrderStatusResponse {
  order_id: string;
  status: OrderStatus;
  amount: number;
  pay_time?: string;
}

// 用户订单列表响应
export interface UserOrdersResponse {
  items: OrderInfo[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 支付记录（兼容旧版本）
export interface PaymentRecord {
  id: string;
  orderId: string;
  packageName: string;
  duration: string;
  amount: number;
  status: OrderStatus;
  createTime: string;
  payTime?: string;
}

// 兑换码相关类型
export interface CouponInfo {
  code: string;
  discount: number; // 折扣金额
  validUntil: string; // 有效期
  isValid: boolean;
}

export interface CouponValidateRequest {
  code: string; // 匹配后端接口
}

export interface CouponValidateResponse {
  valid: boolean;
  discount?: number;
  expiry?: string;
  message: string;
  coupon_info?: {
    id: number;
    code: string;
    type: string;
    value: number;
    max_uses: number;
    used_count: number;
    valid_from?: string;
    valid_until?: string;
    is_active: boolean;
  };
}

// 支付验证兑换码响应
export interface CouponPaymentValidateResponse {
  valid: boolean;
  message: string;
  discount_amount: number;
  final_amount: number;
  coupon_info?: {
    id: number;
    code: string;
    type: string;
    value: number;
    max_uses: number;
    used_count: number;
    valid_from?: string;
    valid_until?: string;
    is_active: boolean;
  };
}

// 兑换码使用请求
export interface CouponRedeemRequest {
  code: string;
  user_id: string;
}

// 兑换码使用响应
export interface CouponRedeemResponse {
  success: boolean;
  message: string;
  coupon_info?: {
    id: number;
    code: string;
    type: string;
    value: number;
    max_uses: number;
    used_count: number;
    valid_from?: string;
    valid_until?: string;
    is_active: boolean;
  };
}

// 微信环境检测结果
export interface WeChatEnvironment {
  isWeChatBrowser: boolean;
  isWeChatMiniProgram: boolean;
  version?: string;
}

// API响应基础类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 用户更新相关类型
export interface UserUpdateRequest {
  nickname?: string;
  gender?: number; // 性别：0=未知, 1=女性, 2=男性
  birthday?: string;
  avatar?: string;
  phone?: string;
}

// 手机号绑定相关类型
export interface SendSmsCodeRequest {
  phone: string;
}

export interface BindPhoneRequest {
  phone: string;
  code: string;
}

// 微信绑定相关类型
export interface BindWeChatRequest {
  wechatCode: string;
}

// 头像上传响应类型
export interface UploadAvatarResponse {
  avatarUrl: string;
}

// 试用激活响应类型
export interface TrialActivationResponse {
  success: boolean;
  message: string;
  userInfo: UserProfile;
}

// 用户资料类型（用于个人中心等场景）- 与UserInfo保持一致
export interface UserProfile extends UserInfo {}

// 后台管理相关类型
export interface AdminOrderListRequest {
  page?: number;
  size?: number;
  status?: OrderStatus;
  payment_method?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
}

export interface AdminOrderStatistics {
  period: {
    start_date: string;
    end_date: string;
  };
  overview: {
    total_orders: number;
    paid_orders: number;
    pending_orders: number;
    failed_orders: number;
    cancelled_orders: number;
    refunded_orders: number;
    success_rate: number;
    total_amount: number;
    total_discount: number;
  };
  daily_stats: Array<{
    date: string;
    orders: number;
    paid_orders: number;
    amount: number;
  }>;
}