<template>
  <div class="streaming-voice-test">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>🎤 流式语音识别测试</h2>
      <div class="status-indicators">
        <span class="status-badge" :class="connectionStatus">
          {{ connectionStatusText }}
        </span>
        <span class="status-badge" :class="streamingStatus">
          {{ streamingStatusText }}
        </span>
      </div>
    </div>

    <!-- 用户登录区域 -->
    <div v-if="!userStore.userInfo" class="login-section">
      <div class="login-card">
        <h3>🔑 需要登录才能测试</h3>
        <button @click="loginTestUser" class="login-btn" :disabled="userStore.loading">
          {{ userStore.loading ? '登录中...' : '登录测试用户' }}
        </button>
      </div>
    </div>

    <!-- 主要测试区域 -->
    <div v-else class="test-area">
      <!-- 录音控制区域 -->
      <div class="recording-control">
        <div class="recording-status">
          <div class="status-circle" :class="{ active: isRecording, streaming: isStreamingActive }">
            <div v-if="isRecording" class="pulse-animation"></div>
            🎤
          </div>
          <div class="status-text">
            <div class="primary-status">
              {{ isRecording ? `录音中 ${recordingDuration.toFixed(1)}s` : '准备就绪' }}
            </div>
            <div class="secondary-status">
              {{ isStreamingActive ? '流式识别激活' : '流式识别未激活' }}
            </div>
          </div>
        </div>

        <div class="control-buttons">
          <button
            v-if="!isRecording"
            @click="startRecording"
            class="record-btn start"
            :disabled="!canStartRecording"
          >
            开始录音
          </button>
          <button
            v-else
            @click="stopRecording"
            class="record-btn stop"
          >
            停止录音
          </button>
        </div>
      </div>

      <!-- 实时数据监控 -->
      <div class="data-monitor">
        <h3>📊 实时数据监控</h3>
        <div class="monitor-grid">
          <div class="monitor-item">
            <div class="monitor-label">音频数据包</div>
            <div class="monitor-value">{{ audioPacketCount }}</div>
          </div>
          <div class="monitor-item">
            <div class="monitor-label">数据传输率</div>
            <div class="monitor-value">{{ dataTransferRate }} KB/s</div>
          </div>
          <div class="monitor-item">
            <div class="monitor-label">WebSocket状态</div>
            <div class="monitor-value">{{ wsConnectionState }}</div>
          </div>
          <div class="monitor-item">
            <div class="monitor-label">最后音频包大小</div>
            <div class="monitor-value">{{ lastAudioPacketSize }} bytes</div>
          </div>
        </div>
      </div>

      <!-- 识别结果显示 -->
      <div class="recognition-results">
        <h3>🔍 识别结果</h3>
        <div class="results-container">
          <div v-if="!currentRecognitionText && !finalRecognitionText" class="no-results">
            暂无识别结果
          </div>
          <div v-if="currentRecognitionText" class="partial-result">
            <div class="result-label">实时识别:</div>
            <div class="result-text">{{ currentRecognitionText }}</div>
          </div>
          <div v-if="finalRecognitionText" class="final-result">
            <div class="result-label">最终结果:</div>
            <div class="result-text">{{ finalRecognitionText }}</div>
            <div class="result-confidence">置信度: {{ recognitionConfidence }}%</div>
          </div>
        </div>
      </div>

      <!-- 音频播放测试区域 -->
      <div class="audio-test-section">
        <h3>🎵 音频播放测试</h3>

        <!-- 格式支持检测 -->
        <div class="format-support">
          <h4>📋 浏览器格式支持检测</h4>
          <div class="support-grid">
            <div v-for="format in audioFormats" :key="format.mime" class="support-item">
              <span class="format-name">{{ format.name }}</span>
              <span class="support-status" :class="format.supported ? 'supported' : 'not-supported'">
                {{ format.supported ? '✅ 支持' : '❌ 不支持' }}
              </span>
              <span class="support-level">{{ format.level }}</span>
            </div>
          </div>
        </div>

        <!-- 录音测试 -->
        <div class="recording-test">
          <h4>🎤 录音测试</h4>
          <div class="test-controls">
            <button
              @click="startTestRecording"
              :disabled="isTestRecording || !canStartRecording"
              class="test-btn record"
            >
              {{ isTestRecording ? '录音中...' : '开始测试录音' }}
            </button>
            <button
              @click="stopTestRecording"
              :disabled="!isTestRecording"
              class="test-btn stop"
            >
              停止录音
            </button>
            <span v-if="isTestRecording" class="recording-time">{{ testRecordingDuration.toFixed(1) }}s</span>
          </div>

          <!-- 录音结果 -->
          <div v-if="testAudioData" class="test-audio-result">
            <h5>录音结果</h5>
            <div class="audio-info">
              <div class="info-item">
                <span class="label">格式:</span>
                <span class="value">{{ testAudioFormat }}</span>
              </div>
              <div class="info-item">
                <span class="label">大小:</span>
                <span class="value">{{ formatFileSize(testAudioSize) }}</span>
              </div>
              <div class="info-item">
                <span class="label">时长:</span>
                <span class="value">{{ testRecordingDuration.toFixed(1) }}s</span>
              </div>
            </div>

            <!-- 播放测试按钮 -->
            <div class="playback-tests">
              <h5>播放测试</h5>
              <div class="test-buttons">
                <button @click="testLocalBlobPlayback" class="test-btn play">
                  🎵 本地Blob播放
                </button>
                <button @click="testDataUrlPlayback" class="test-btn play">
                  🎵 Data URL播放
                </button>
                <button @click="testUploadAndPlay" class="test-btn play">
                  🎵 上传后播放
                </button>
                <button @click="testMessageItemPlayback" class="test-btn play">
                  🎵 MessageItem播放
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 测试样本 -->
        <div class="test-samples">
          <h4>🧪 测试样本</h4>
          <div class="sample-list">
            <div v-for="sample in testSamples" :key="sample.id" class="sample-item">
              <div class="sample-info">
                <span class="sample-name">{{ sample.name }}</span>
                <span class="sample-type">{{ sample.type }}</span>
                <span class="sample-format">{{ sample.format }}</span>
              </div>
              <div class="sample-controls">
                <button @click="testSamplePlayback(sample)" class="test-btn play">
                  播放测试
                </button>
                <span v-if="sample.status" class="test-status" :class="sample.status">
                  {{ sample.statusText }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 播放状态监控 -->
        <div class="playback-monitor">
          <h4>📊 播放状态监控</h4>
          <div class="monitor-content">
            <div v-if="currentTestAudio" class="audio-status">
              <div class="status-item">
                <span class="label">当前播放:</span>
                <span class="value">{{ currentTestAudioName }}</span>
              </div>
              <div class="status-item">
                <span class="label">播放状态:</span>
                <span class="value">{{ audioPlaybackState }}</span>
              </div>
              <div class="status-item">
                <span class="label">当前时间:</span>
                <span class="value">{{ currentTime.toFixed(1) }}s</span>
              </div>
              <div class="status-item">
                <span class="label">总时长:</span>
                <span class="value">{{ duration.toFixed(1) }}s</span>
              </div>
              <div class="status-item">
                <span class="label">播放进度:</span>
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                </div>
                <span class="progress-text">{{ progressPercentage.toFixed(1) }}%</span>
              </div>
            </div>
            <div v-else class="no-audio">
              暂无播放中的音频
            </div>
          </div>
        </div>
      </div>

      <!-- 消息历史 -->
      <div class="message-history">
        <h3>💬 消息历史</h3>
        <div class="messages-container" ref="messagesContainer">
          <div v-if="messages.length === 0" class="no-messages">
            暂无消息记录
          </div>
          <div
            v-for="(message, index) in messages"
            :key="index"
            class="message-item"
            :class="message.role"
          >
            <div class="message-header">
              <span class="message-role">{{ message.role === 'user' ? '用户' : 'AI' }}</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              <span v-if="message.type === 'audio'" class="message-type">🎵 语音</span>
            </div>
            <div class="message-content">{{ message.content }}</div>

            <!-- 语音消息播放控件 -->
            <div v-if="message.type === 'audio' && message.audioUrl" class="audio-player">
              <button
                @click="playAudio(message.audioUrl, message.id)"
                class="play-btn"
                :disabled="playingAudioId === message.id"
              >
                {{ playingAudioId === message.id ? '🔊 播放中...' : '▶️ 播放语音' }}
              </button>
              <span class="audio-duration">{{ message.audioDuration }}秒</span>
            </div>

            <div v-if="message.transcription" class="message-transcription">
              识别文本: {{ message.transcription }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 调试日志面板 -->
    <div class="debug-panel" :class="{ collapsed: !showDebugPanel }">
      <div class="debug-header" @click="toggleDebugPanel">
        <span class="debug-title">🔧 调试日志</span>
        <span class="debug-toggle">{{ showDebugPanel ? '▼' : '▲' }}</span>
      </div>
      <div v-if="showDebugPanel" class="debug-content">
        <div class="debug-controls">
          <button @click="clearDebugLogs" class="clear-btn">清空日志</button>
          <label class="auto-scroll-label">
            <input type="checkbox" v-model="autoScrollDebug" />
            自动滚动
          </label>
        </div>
        <div class="debug-logs" ref="debugLogsContainer">
          <div
            v-for="(log, index) in debugLogs"
            :key="index"
            class="debug-log-item"
            :class="log.level"
          >
            <span class="log-time">{{ log.timestamp }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="debugLogs.length === 0" class="no-logs">
            暂无调试日志
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { AudioRecorder } from '@/utils/audioRecorder'
import { WebSocketChatClient } from '@/utils/websocketClient'
import { appConfig } from '@/config/env'
import type { ChatMessage } from '@/types/api'

// 用户store
const userStore = useUserStore()

// 核心组件引用
const audioRecorder = ref<AudioRecorder | null>(null)
const wsClient = ref<WebSocketChatClient | null>(null)

// 录音状态
const isRecording = ref(false)
const recordingDuration = ref(0)
const recordingTimer = ref<number | null>(null)

// 流式识别状态
const isStreamingActive = ref(false)
const currentRecognitionText = ref('')
const finalRecognitionText = ref('')
const recognitionConfidence = ref(0)

// 数据监控
const audioPacketCount = ref(0)
const dataTransferRate = ref(0)
const lastAudioPacketSize = ref(0)
const dataTransferHistory = ref<Array<{ timestamp: number, size: number }>>([])

// 消息历史
const messages = ref<ChatMessage[]>([])
const messagesContainer = ref<HTMLElement>()

// 音频播放状态
const playingAudioId = ref<string | null>(null)
const currentAudio = ref<HTMLAudioElement | null>(null)

// 音频测试相关状态
const isTestRecording = ref(false)
const testRecordingDuration = ref(0)
const testRecordingTimer = ref<number | null>(null)
const testAudioData = ref<{
  blob: Blob | null
  dataUrl: string | null
  base64: string | null
  format: string
  size: number
}>({
  blob: null,
  dataUrl: null,
  base64: null,
  format: '',
  size: 0
})

// 音频格式支持检测
const audioFormats = ref([
  { name: 'WebM', mime: 'audio/webm', supported: false, level: '' },
  { name: 'WebM+Opus', mime: 'audio/webm;codecs=opus', supported: false, level: '' },
  { name: 'WAV', mime: 'audio/wav', supported: false, level: '' },
  { name: 'MP3', mime: 'audio/mp3', supported: false, level: '' },
  { name: 'MP4', mime: 'audio/mp4', supported: false, level: '' },
  { name: 'OGG', mime: 'audio/ogg', supported: false, level: '' },
  { name: 'AAC', mime: 'audio/aac', supported: false, level: '' }
])

// 测试样本
const testSamples = ref([
  {
    id: 'sample1',
    name: '测试WebM样本',
    type: 'Data URL',
    format: 'webm',
    url: '',
    status: null as string | null,
    statusText: ''
  },
  {
    id: 'sample2',
    name: '测试WAV样本',
    type: 'Data URL',
    format: 'wav',
    url: '',
    status: null as string | null,
    statusText: ''
  },
  {
    id: 'sample3',
    name: '服务器文件样本',
    type: 'File URL',
    format: 'unknown',
    url: 'https://www.airelief.cn/api/v1/airelief/files/audio/test.wav',
    status: null as string | null,
    statusText: ''
  }
])

// 当前测试音频状态
const currentTestAudio = ref<HTMLAudioElement | null>(null)
const currentTestAudioName = ref('')
const currentTime = ref(0)
const duration = ref(0)
const audioPlaybackState = ref('stopped')

// 计算属性
const testAudioFormat = computed(() => testAudioData.value.format)
const testAudioSize = computed(() => testAudioData.value.size)
const progressPercentage = computed(() => {
  if (duration.value === 0) return 0
  return (currentTime.value / duration.value) * 100
})

// 调试日志
const debugLogs = ref<Array<{
  timestamp: string
  level: 'info' | 'warn' | 'error' | 'success'
  message: string
}>>([])
const showDebugPanel = ref(true)
const autoScrollDebug = ref(true)
const debugLogsContainer = ref<HTMLElement>()

// 计算属性
const connectionStatus = computed(() => {
  if (!wsClient.value) return 'disconnected'
  return wsClient.value.isConnected() ? 'connected' : 'disconnected'
})

const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '🟢 已连接'
    case 'disconnected': return '🔴 未连接'
    default: return '🟡 连接中'
  }
})

const streamingStatus = computed(() => {
  return isStreamingActive.value ? 'active' : 'inactive'
})

const streamingStatusText = computed(() => {
  return isStreamingActive.value ? '🔄 流式激活' : '⏸️ 流式未激活'
})

const wsConnectionState = computed(() => {
  if (!wsClient.value) return 'NULL'
  const ws = (wsClient.value as any).ws
  if (!ws) return 'NULL'
  
  switch (ws.readyState) {
    case WebSocket.CONNECTING: return 'CONNECTING'
    case WebSocket.OPEN: return 'OPEN'
    case WebSocket.CLOSING: return 'CLOSING'
    case WebSocket.CLOSED: return 'CLOSED'
    default: return 'UNKNOWN'
  }
})

const canStartRecording = computed(() => {
  return audioRecorder.value && wsClient.value && wsClient.value.isConnected()
})

// 调试日志函数
const addDebugLog = (level: 'info' | 'warn' | 'error' | 'success', message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  debugLogs.value.push({
    timestamp,
    level,
    message
  })

  // 限制日志数量
  if (debugLogs.value.length > 200) {
    debugLogs.value.shift()
  }

  // 自动滚动
  if (autoScrollDebug.value) {
    nextTick(() => {
      if (debugLogsContainer.value) {
        debugLogsContainer.value.scrollTop = debugLogsContainer.value.scrollHeight
      }
    })
  }
}

// 清空调试日志
const clearDebugLogs = () => {
  debugLogs.value = []
}

// 切换调试面板
const toggleDebugPanel = () => {
  showDebugPanel.value = !showDebugPanel.value
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 检测音频格式支持
const detectAudioSupport = () => {
  const audio = document.createElement('audio')
  audioFormats.value.forEach(format => {
    const support = audio.canPlayType(format.mime)
    format.supported = support !== ''
    format.level = support || 'not supported'
  })
  addDebugLog('info', '音频格式支持检测完成')
}

// 开始测试录音
const startTestRecording = async () => {
  if (!audioRecorder.value) {
    addDebugLog('error', '录音器未初始化')
    showToast('录音器未初始化')
    return
  }

  try {
    addDebugLog('info', '开始测试录音...')
    isTestRecording.value = true
    testRecordingDuration.value = 0

    // 清空之前的录音数据
    testAudioData.value = {
      blob: null,
      dataUrl: null,
      base64: null,
      format: '',
      size: 0
    }

    // 开始录音计时
    testRecordingTimer.value = setInterval(() => {
      testRecordingDuration.value += 0.1
    }, 100)

    // 开始录音
    await audioRecorder.value.startRecording()
    addDebugLog('success', '测试录音已开始')

  } catch (error: any) {
    addDebugLog('error', `开始测试录音失败: ${error.message}`)
    showToast(`录音失败: ${error.message}`)
    isTestRecording.value = false
    if (testRecordingTimer.value) {
      clearInterval(testRecordingTimer.value)
      testRecordingTimer.value = null
    }
  }
}

// 停止测试录音
const stopTestRecording = async () => {
  if (!audioRecorder.value || !isTestRecording.value) {
    return
  }

  try {
    addDebugLog('info', '停止测试录音...')

    // 停止录音计时
    if (testRecordingTimer.value) {
      clearInterval(testRecordingTimer.value)
      testRecordingTimer.value = null
    }

    // 停止录音
    const result = await audioRecorder.value.stopRecording()
    isTestRecording.value = false

    if (result && result.audioBlob) {
      // 保存录音数据，保持与正常聊天流程一致的格式
      testAudioData.value.blob = result.audioBlob
      testAudioData.value.format = result.format || 'webm'
      testAudioData.value.size = result.audioBlob.size

      // 生成Data URL（与后端返回格式一致）
      const reader = new FileReader()
      reader.onload = () => {
        testAudioData.value.dataUrl = reader.result as string
        testAudioData.value.base64 = (reader.result as string).split(',')[1]

        // 模拟后端处理流程，生成与正常聊天一致的音频数据
        simulateAudioProcessing(result)

        addDebugLog('success', `测试录音完成: ${testAudioData.value.format}, ${formatFileSize(testAudioData.value.size)}`)
      }
      reader.readAsDataURL(result.audioBlob)
    }

  } catch (error: any) {
    addDebugLog('error', `停止测试录音失败: ${error.message}`)
    showToast(`停止录音失败: ${error.message}`)
    isTestRecording.value = false
  }
}

// 模拟音频处理流程（不调用语音识别，但保持数据格式一致）
const simulateAudioProcessing = (recordingResult: any) => {
  addDebugLog('info', '模拟音频处理流程（跳过语音识别）...')

  // 模拟后端会做的音频处理
  const audioData = {
    // 模拟转换为base64格式（与WebSocket发送格式一致）
    audio_data: testAudioData.value.base64,
    audio_format: testAudioData.value.format,
    audio_duration: testRecordingDuration.value,

    // 模拟音频文件保存（与后端upload API格式一致）
    file_url: testAudioData.value.dataUrl, // 使用Data URL模拟文件URL

    // 模拟语音识别结果（测试用）
    transcription: '这是测试录音，跳过了语音识别',

    // 模拟消息ID
    message_id: `test_${Date.now()}`,
    timestamp: new Date().toISOString()
  }

  // 更新测试样本，添加当前录音作为测试样本
  const newSample = {
    id: `recorded_${Date.now()}`,
    name: `录音测试样本 (${testAudioData.value.format})`,
    type: 'Data URL',
    format: testAudioData.value.format,
    url: testAudioData.value.dataUrl,
    status: null as string | null,
    statusText: '',
    // 额外的测试数据
    audioData: audioData,
    isRecorded: true
  }

  testSamples.value.unshift(newSample)

  addDebugLog('success', `音频处理模拟完成，已添加到测试样本`)
  showToast('录音已添加到测试样本，可以进行播放测试')
}

// 创建测试音频对象并设置监听
const createTestAudio = (url: string, name: string): HTMLAudioElement => {
  const audio = new Audio(url)

  // 停止之前的音频
  if (currentTestAudio.value) {
    currentTestAudio.value.pause()
    currentTestAudio.value = null
  }

  currentTestAudio.value = audio
  currentTestAudioName.value = name
  currentTime.value = 0
  duration.value = 0
  audioPlaybackState.value = 'loading'

  // 设置详细的事件监听
  audio.onloadstart = () => {
    addDebugLog('info', `[${name}] 音频开始加载`)
    audioPlaybackState.value = 'loading'
  }

  audio.onloadedmetadata = () => {
    duration.value = audio.duration || 0
    addDebugLog('info', `[${name}] 音频元数据加载完成，时长: ${duration.value.toFixed(1)}s`)
  }

  audio.oncanplay = () => {
    addDebugLog('success', `[${name}] 音频可以播放`)
    audioPlaybackState.value = 'ready'
  }

  audio.onplay = () => {
    addDebugLog('success', `[${name}] 音频开始播放`)
    audioPlaybackState.value = 'playing'
  }

  audio.onpause = () => {
    addDebugLog('info', `[${name}] 音频暂停`)
    audioPlaybackState.value = 'paused'
  }

  audio.onended = () => {
    addDebugLog('success', `[${name}] 音频播放完成`)
    audioPlaybackState.value = 'ended'
    currentTestAudio.value = null
    currentTestAudioName.value = ''
  }

  audio.ontimeupdate = () => {
    currentTime.value = audio.currentTime || 0
  }

  audio.onerror = () => {
    const error = audio.error
    let errorMsg = 'unknown'
    if (error) {
      switch (error.code) {
        case MediaError.MEDIA_ERR_ABORTED:
          errorMsg = 'ABORTED(用户中止)'
          break
        case MediaError.MEDIA_ERR_NETWORK:
          errorMsg = 'NETWORK(网络错误)'
          break
        case MediaError.MEDIA_ERR_DECODE:
          errorMsg = 'DECODE(解码错误)'
          break
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          errorMsg = 'NOT_SUPPORTED(格式不支持)'
          break
        default:
          errorMsg = `CODE_${error.code}`
      }
    }
    addDebugLog('error', `[${name}] 音频播放错误: ${errorMsg}`)
    audioPlaybackState.value = 'error'
    showToast(`播放失败: ${errorMsg}`)
  }

  return audio
}

// 测试本地Blob播放
const testLocalBlobPlayback = async () => {
  if (!testAudioData.value.blob) {
    addDebugLog('error', '没有可用的录音数据')
    showToast('请先录制音频')
    return
  }

  try {
    const url = URL.createObjectURL(testAudioData.value.blob)
    const audio = createTestAudio(url, `本地Blob(${testAudioData.value.format})`)

    await audio.play()
    addDebugLog('success', '本地Blob播放启动成功')

    // 播放结束后清理URL
    audio.onended = () => {
      URL.revokeObjectURL(url)
      addDebugLog('info', '本地Blob URL已清理')
      audioPlaybackState.value = 'ended'
      currentTestAudio.value = null
      currentTestAudioName.value = ''
    }

  } catch (error: any) {
    addDebugLog('error', `本地Blob播放失败: ${error.message}`)
    showToast(`播放失败: ${error.message}`)
  }
}

// 测试Data URL播放
const testDataUrlPlayback = async () => {
  if (!testAudioData.value.dataUrl) {
    addDebugLog('error', '没有可用的Data URL')
    showToast('请先录制音频')
    return
  }

  try {
    const audio = createTestAudio(testAudioData.value.dataUrl, `Data URL(${testAudioData.value.format})`)
    await audio.play()
    addDebugLog('success', 'Data URL播放启动成功')

  } catch (error: any) {
    addDebugLog('error', `Data URL播放失败: ${error.message}`)
    showToast(`播放失败: ${error.message}`)
  }
}

// 播放音频
const playAudio = async (audioUrl: string, messageId: string) => {
  try {
    addDebugLog('info', `开始播放音频: ${audioUrl}`)

    // 如果有正在播放的音频，先停止
    if (currentAudio.value) {
      currentAudio.value.pause()
      currentAudio.value = null
      playingAudioId.value = null
    }

    // 创建新的音频对象
    const audio = new Audio(audioUrl)
    currentAudio.value = audio
    playingAudioId.value = messageId

    // 设置音频事件监听
    audio.onloadstart = () => {
      addDebugLog('info', '音频开始加载...')
    }

    audio.oncanplay = () => {
      addDebugLog('success', '音频可以播放')
    }

    audio.onplay = () => {
      addDebugLog('success', '音频开始播放')
    }

    audio.onended = () => {
      addDebugLog('success', '音频播放完成')
      playingAudioId.value = null
      currentAudio.value = null
    }

    audio.onerror = (error) => {
      addDebugLog('error', `音频播放错误: ${error}`)
      showToast('音频播放失败')
      playingAudioId.value = null
      currentAudio.value = null
    }

    // 开始播放
    await audio.play()

  } catch (error: any) {
    addDebugLog('error', `播放音频失败: ${error.message}`)
    showToast(`播放失败: ${error.message}`)
    playingAudioId.value = null
    currentAudio.value = null
  }
}

// 测试上传后播放（模拟正常聊天流程）
const testUploadAndPlay = async () => {
  if (!testAudioData.value.blob) {
    addDebugLog('error', '没有可用的录音数据')
    showToast('请先录制音频')
    return
  }

  try {
    addDebugLog('info', '开始上传音频文件（模拟正常聊天流程）...')
    showToast('正在上传音频...')

    // 创建FormData（与正常聊天流程一致）
    const formData = new FormData()
    formData.append('file', testAudioData.value.blob, `test_audio.${testAudioData.value.format}`)
    formData.append('file_type', 'audio')

    // 上传文件到后端
    const response = await fetch('/api/v1/airelief/upload', {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error(`上传失败: ${response.status}`)
    }

    const result = await response.json()
    if (result.success && result.data?.file_url) {
      addDebugLog('success', `文件上传成功: ${result.data.file_url}`)

      // 模拟后端返回的语音消息格式（与WebSocket回调一致）
      const mockVoiceMessage = {
        message_id: `test_upload_${Date.now()}`,
        role: 'user',
        content: '这是上传测试音频，跳过了语音识别',
        type: 'audio',
        timestamp: new Date().toISOString(),
        audioDuration: testRecordingDuration.value,
        audioUrl: result.data.file_url, // 服务器端音频URL
        transcription: '这是上传测试音频，跳过了语音识别',
        // 本地音频数据（用于对比测试）
        localAudioBlob: testAudioData.value.blob,
        localAudioData: testAudioData.value.dataUrl,
        isLocalOnly: false
      }

      addDebugLog('info', `模拟语音消息格式: ${JSON.stringify(mockVoiceMessage, null, 2)}`)

      // 播放上传后的文件
      const audio = createTestAudio(result.data.file_url, `上传文件(${testAudioData.value.format})`)
      await audio.play()
      addDebugLog('success', '上传文件播放启动成功')

      // 添加到测试样本
      const uploadSample = {
        id: `uploaded_${Date.now()}`,
        name: `上传文件测试 (${testAudioData.value.format})`,
        type: 'File URL',
        format: testAudioData.value.format,
        url: result.data.file_url,
        status: 'success' as string | null,
        statusText: '上传成功',
        audioData: mockVoiceMessage,
        isUploaded: true
      }

      testSamples.value.unshift(uploadSample)
      showToast('文件上传成功，已添加到测试样本')

    } else {
      throw new Error(result.message || '上传失败')
    }

  } catch (error: any) {
    addDebugLog('error', `上传播放测试失败: ${error.message}`)
    showToast(`上传失败: ${error.message}`)
  }
}

// 测试MessageItem播放逻辑（完全模拟正常聊天流程）
const testMessageItemPlayback = async () => {
  if (!testAudioData.value.dataUrl) {
    addDebugLog('error', '没有可用的音频数据')
    showToast('请先录制音频')
    return
  }

  try {
    addDebugLog('info', '使用MessageItem播放逻辑测试（完全模拟正常聊天）...')

    // 模拟完整的ChatMessage对象（与正常聊天中的消息格式完全一致）
    const mockChatMessage = {
      id: `test_message_${Date.now()}`,
      role: 'user' as const,
      content: '这是测试语音消息',
      type: 'audio' as const,
      timestamp: Date.now(),
      audioDuration: testRecordingDuration.value,
      audioUrl: testAudioData.value.dataUrl, // 模拟服务器URL
      transcription: '这是测试语音消息，跳过了语音识别',
      localId: null, // 微信环境下的localId
      sessionId: 'test_session',

      // 本地音频缓存数据（新增的字段）
      localAudioData: testAudioData.value.dataUrl,
      localAudioBlob: testAudioData.value.blob,
      isLocalOnly: false, // 模拟已上传到服务器
      serverMessageId: `server_msg_${Date.now()}`
    }

    addDebugLog('info', `模拟ChatMessage对象: ${JSON.stringify({
      ...mockChatMessage,
      localAudioBlob: mockChatMessage.localAudioBlob ? '有Blob数据' : '无Blob数据'
    }, null, 2)}`)

    // 完全按照MessageItem.vue中的播放优先级逻辑进行测试
    addDebugLog('info', '开始MessageItem播放优先级测试...')

    // 1. 优先使用本地Blob（最佳体验）
    if (mockChatMessage.localAudioBlob) {
      addDebugLog('info', 'MessageItem逻辑: 优先级1 - 使用本地Blob播放')
      try {
        const url = URL.createObjectURL(mockChatMessage.localAudioBlob)
        const audio = createTestAudio(url, 'MessageItem-LocalBlob')

        // 设置清理回调
        audio.onended = () => {
          URL.revokeObjectURL(url)
          addDebugLog('info', 'MessageItem逻辑: 本地Blob URL已清理')
          audioPlaybackState.value = 'ended'
          currentTestAudio.value = null
          currentTestAudioName.value = ''
        }

        await audio.play()
        addDebugLog('success', 'MessageItem逻辑: 本地Blob播放成功')
        return
      } catch (error: any) {
        addDebugLog('error', `MessageItem逻辑: 本地Blob播放失败，尝试下一个选项: ${error.message}`)
      }
    }

    // 2. 使用本地Data URL
    if (mockChatMessage.localAudioData) {
      addDebugLog('info', 'MessageItem逻辑: 优先级2 - 使用本地Data URL播放')
      try {
        const audio = createTestAudio(mockChatMessage.localAudioData, 'MessageItem-DataURL')
        await audio.play()
        addDebugLog('success', 'MessageItem逻辑: 本地Data URL播放成功')
        return
      } catch (error: any) {
        addDebugLog('error', `MessageItem逻辑: 本地Data URL播放失败，尝试下一个选项: ${error.message}`)
      }
    }

    // 3. 使用微信localId（如果在微信环境）
    if (mockChatMessage.localId && window.wx) {
      addDebugLog('info', 'MessageItem逻辑: 优先级3 - 使用微信localId播放')
      try {
        // 这里只是模拟，实际微信播放需要真实的localId
        addDebugLog('info', 'MessageItem逻辑: 微信环境播放（模拟）')
        showToast('微信环境播放（模拟）')
        return
      } catch (error: any) {
        addDebugLog('error', `MessageItem逻辑: 微信播放失败，尝试下一个选项: ${error.message}`)
      }
    }

    // 4. 使用服务器audioUrl
    if (mockChatMessage.audioUrl) {
      addDebugLog('info', 'MessageItem逻辑: 优先级4 - 使用服务器URL播放')
      try {
        const audio = createTestAudio(mockChatMessage.audioUrl, 'MessageItem-ServerURL')
        await audio.play()
        addDebugLog('success', 'MessageItem逻辑: 服务器URL播放成功')
        return
      } catch (error: any) {
        addDebugLog('error', `MessageItem逻辑: 服务器URL播放失败: ${error.message}`)
      }
    }

    // 5. 没有可播放的音频
    addDebugLog('warn', 'MessageItem逻辑: 没有可播放的音频，显示转录文本')
    showToast(`语音内容: ${mockChatMessage.transcription}`)

  } catch (error: any) {
    addDebugLog('error', `MessageItem播放测试失败: ${error.message}`)
    showToast(`播放失败: ${error.message}`)
  }
}

// 测试样本播放
const testSamplePlayback = async (sample: any) => {
  try {
    addDebugLog('info', `测试样本播放: ${sample.name}`)
    sample.status = 'testing'
    sample.statusText = '测试中...'

    let audioUrl = sample.url

    // 如果是空URL，生成测试数据
    if (!audioUrl && sample.format) {
      if (testAudioData.value.dataUrl && testAudioData.value.format === sample.format) {
        audioUrl = testAudioData.value.dataUrl
      } else {
        // 生成测试音频数据
        audioUrl = generateTestAudioData(sample.format)
      }
    }

    if (!audioUrl) {
      throw new Error('无法生成测试音频数据')
    }

    const audio = createTestAudio(audioUrl, sample.name)
    await audio.play()

    sample.status = 'success'
    sample.statusText = '播放成功'
    addDebugLog('success', `样本播放成功: ${sample.name}`)

  } catch (error: any) {
    sample.status = 'error'
    sample.statusText = `失败: ${error.message}`
    addDebugLog('error', `样本播放失败: ${sample.name} - ${error.message}`)
    showToast(`播放失败: ${error.message}`)
  }
}

// 生成测试音频数据
const generateTestAudioData = (format: string): string => {
  // 这里可以生成简单的测试音频数据
  // 为了简化，返回一个基本的音频数据URL
  const sampleRate = 44100
  const duration = 1 // 1秒
  const samples = sampleRate * duration
  const buffer = new ArrayBuffer(samples * 2)
  const view = new DataView(buffer)

  // 生成简单的正弦波
  for (let i = 0; i < samples; i++) {
    const sample = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.5
    view.setInt16(i * 2, sample * 32767, true)
  }

  // 转换为base64
  const bytes = new Uint8Array(buffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  const base64 = btoa(binary)

  return `data:audio/${format};base64,${base64}`
}

// 计算数据传输率
const updateDataTransferRate = () => {
  const now = Date.now()
  const oneSecondAgo = now - 1000

  // 过滤最近1秒的数据
  const recentData = dataTransferHistory.value.filter(item => item.timestamp > oneSecondAgo)

  // 计算总字节数
  const totalBytes = recentData.reduce((sum, item) => sum + item.size, 0)

  // 转换为KB/s
  dataTransferRate.value = Math.round(totalBytes / 1024 * 10) / 10

  // 清理旧数据
  dataTransferHistory.value = dataTransferHistory.value.filter(item => item.timestamp > now - 5000)
}

// 登录测试用户
const loginTestUser = async () => {
  try {
    addDebugLog('info', '开始登录测试用户...')
    showToast('正在登录测试用户...')

    // 使用测试用户登录方法，绕过微信认证（限制用户ID长度为16字符）
    const timestamp = Date.now().toString().slice(-6) // 取时间戳后6位
    const testUserId = `stream${timestamp}` // 最多12字符
    const loginResult = await userStore.testUserLogin(testUserId)

    if (loginResult && loginResult.userInfo) {
      addDebugLog('success', `测试用户登录成功: ${loginResult.userInfo.nickname}`)
      showToast(`登录成功: ${loginResult.userInfo.nickname}`)

      // 登录成功后初始化WebSocket和录音器
      await initializeComponents()
    } else {
      addDebugLog('error', '测试用户登录失败')
      showToast('登录失败，请重试')
    }
  } catch (error: any) {
    addDebugLog('error', `测试用户登录异常: ${error.message}`)
    showToast(`登录失败: ${error.message}`)
  }
}

// 初始化组件
const initializeComponents = async () => {
  try {
    addDebugLog('info', '开始初始化组件...')

    // 检测音频格式支持
    detectAudioSupport()

    // 初始化WebSocket
    await initWebSocket()

    // 初始化录音器
    await initAudioRecorder()

    addDebugLog('success', '所有组件初始化完成')
  } catch (error: any) {
    addDebugLog('error', `组件初始化失败: ${error.message}`)
  }
}

// 初始化WebSocket
const initWebSocket = async () => {
  try {
    const userId = userStore.userInfo?.user_id
    if (!userId) {
      throw new Error('用户ID不存在')
    }

    addDebugLog('info', `初始化WebSocket连接，用户ID: ${userId}`)

    // 创建WebSocket客户端
    wsClient.value = new WebSocketChatClient({
      url: `${appConfig.wsBaseUrl}/api/v1/airelief/chat/ws/${userId}`,
      userId: userId
    })

    // 设置事件监听
    setupWebSocketEvents()

    // 连接WebSocket
    await wsClient.value.connect()

    addDebugLog('success', 'WebSocket连接成功')
  } catch (error: any) {
    addDebugLog('error', `WebSocket初始化失败: ${error.message}`)
    throw error
  }
}

// 设置WebSocket事件监听
const setupWebSocketEvents = () => {
  if (!wsClient.value) return

  // 连接成功
  wsClient.value.onConnected(() => {
    addDebugLog('success', 'WebSocket连接已建立')
  })

  // 连接断开
  wsClient.value.onDisconnected(() => {
    addDebugLog('warn', 'WebSocket连接已断开')
    isStreamingActive.value = false
  })

  // 连接错误
  wsClient.value.onError((error) => {
    addDebugLog('error', `WebSocket错误: ${error}`)
  })

  // 系统消息处理
  wsClient.value.onSystemMessage((type, data) => {
    addDebugLog('info', `收到系统消息: ${type}`)
    handleWebSocketMessage(type, data)
  })

  // AI消息处理
  wsClient.value.onMessage((message) => {
    addDebugLog('info', `收到AI消息: ${message.content}`)
    // 转换为本地消息格式
    const localMessage: ChatMessage = {
      id: message.message_id || `ai_${Date.now()}`,
      role: message.role,
      content: message.content,
      type: message.message_type || 'text',
      timestamp: Date.now(),
      transcription: message.transcription,
      audioDuration: message.audio_duration
    }
    messages.value.push(localMessage)
    scrollToMessagesBottom()
  })
}

// 处理WebSocket消息
const handleWebSocketMessage = (type: string, data: any) => {
  addDebugLog('info', `收到WebSocket消息: ${type}`)

  // 详细记录消息内容
  if (data && Object.keys(data).length > 0) {
    addDebugLog('info', `消息数据: ${JSON.stringify(data)}`)
  }

  switch (type) {
    case 'streaming_started':
      addDebugLog('success', '流式识别已启动')
      isStreamingActive.value = true
      showToast('流式识别已启动')
      break

    case 'streaming_result':
      addDebugLog('success', `收到流式识别结果: ${JSON.stringify(data)}`)
      handleStreamingResult(data)
      break

    case 'streaming_ended':
      addDebugLog('success', '流式识别已结束')
      isStreamingActive.value = false
      showToast('流式识别已结束')
      break

    case 'voice_message_sent':
      addDebugLog('success', `语音消息已发送: ${JSON.stringify(data)}`)
      // 添加用户消息到历史
      const userMessage: ChatMessage = {
        id: data.message_id || `msg_${Date.now()}`,
        role: 'user',
        content: data.transcription || '语音消息',
        type: 'audio',
        timestamp: Date.now(),
        transcription: data.transcription,
        audioDuration: data.audioDuration,
        audioUrl: data.audioUrl // 重要：包含音频文件URL用于播放
      }
      messages.value.push(userMessage)
      scrollToMessagesBottom()
      break

    case 'error':
      addDebugLog('error', `服务器错误: ${data.message || JSON.stringify(data)}`)
      showToast(`错误: ${data.message || '未知错误'}`)
      break

    default:
      addDebugLog('warn', `未处理的消息类型: ${type}, 数据: ${JSON.stringify(data)}`)
  }
}

// 处理流式识别结果
const handleStreamingResult = (result: any) => {
  const { text, is_final, confidence } = result

  addDebugLog('info', `流式识别结果: ${text} (final: ${is_final})`)

  if (is_final) {
    // 最终结果
    finalRecognitionText.value = text
    recognitionConfidence.value = Math.round((confidence || 0) * 100)
    currentRecognitionText.value = '' // 清空实时结果
    addDebugLog('success', `最终识别结果: ${text}`)
  } else {
    // 实时结果
    currentRecognitionText.value = text
  }
}

// 初始化录音器
const initAudioRecorder = async () => {
  try {
    addDebugLog('info', '初始化录音器...')

    // 创建录音器实例
    audioRecorder.value = new AudioRecorder({
      sampleRate: 16000,
      channels: 1,
      maxDuration: 60,
      minDuration: 1
    })

    // 设置事件监听
    setupAudioRecorderEvents()

    // 请求麦克风权限
    const hasPermission = await audioRecorder.value.requestPermission()
    if (!hasPermission) {
      throw new Error('麦克风权限被拒绝')
    }

    addDebugLog('success', '录音器初始化成功')
  } catch (error: any) {
    addDebugLog('error', `录音器初始化失败: ${error.message}`)
    throw error
  }
}

// 设置录音器事件监听
const setupAudioRecorderEvents = () => {
  if (!audioRecorder.value) return

  // 录音开始
  audioRecorder.value.onStart(() => {
    addDebugLog('success', '录音已开始')
    startRecordingTimer()
  })

  // 录音结束
  audioRecorder.value.onStop((result) => {
    addDebugLog('success', `录音已结束，时长: ${result.duration}s`)
    stopRecordingTimer()
  })

  // 录音错误
  audioRecorder.value.onError((error) => {
    addDebugLog('error', `录音错误: ${error}`)
    stopRecordingTimer()
    isRecording.value = false
  })

  // 实时音频数据
  audioRecorder.value.onRealTimeData((audioData) => {
    handleRealTimeAudioData(audioData)
  })
}

// 处理实时音频数据
const handleRealTimeAudioData = (audioData: string) => {
  if (!isStreamingActive.value || !wsClient.value || !wsClient.value.isConnected()) {
    addDebugLog('warn', `跳过音频数据发送 - 流式激活: ${isStreamingActive.value}, WebSocket连接: ${wsClient.value?.isConnected()}`)
    return
  }

  try {
    // 发送音频数据到WebSocket
    const success = wsClient.value.sendStreamingAudio(audioData)

    if (success) {
      // 更新数据监控
      audioPacketCount.value++
      lastAudioPacketSize.value = Math.round(audioData.length * 0.75) // base64解码后的大小估算

      // 记录数据传输历史
      dataTransferHistory.value.push({
        timestamp: Date.now(),
        size: lastAudioPacketSize.value
      })

      // 更新传输率
      updateDataTransferRate()

      // 每10个包记录一次详细信息，避免日志过多
      if (audioPacketCount.value % 10 === 0) {
        addDebugLog('info', `已发送${audioPacketCount.value}个音频包，最新包大小: ${lastAudioPacketSize.value} bytes`)
      }
    } else {
      addDebugLog('warn', '音频数据发送失败')
    }
  } catch (error: any) {
    addDebugLog('error', `处理实时音频数据失败: ${error.message}`)
  }
}

// 开始录音
const startRecording = async () => {
  try {
    addDebugLog('info', '准备开始录音...')

    // 检查前置条件
    if (!audioRecorder.value) {
      throw new Error('录音器未初始化')
    }

    if (!wsClient.value || !wsClient.value.isConnected()) {
      throw new Error('WebSocket未连接')
    }

    // 重置状态
    currentRecognitionText.value = ''
    finalRecognitionText.value = ''
    recognitionConfidence.value = 0
    audioPacketCount.value = 0
    dataTransferRate.value = 0
    lastAudioPacketSize.value = 0

    // 启动流式识别
    addDebugLog('info', '启动流式识别...')
    const streamingStarted = wsClient.value.startStreamingRecognition()

    if (!streamingStarted) {
      throw new Error('启动流式识别失败')
    }

    // 等待流式识别启动确认（最多等待3秒）
    addDebugLog('info', '等待流式识别启动确认...')

    let waitCount = 0
    const maxWait = 30 // 最多等待3秒（30 * 100ms）

    while (!isStreamingActive.value && waitCount < maxWait) {
      await new Promise(resolve => setTimeout(resolve, 100))
      waitCount++
    }

    if (!isStreamingActive.value) {
      addDebugLog('warn', '流式识别启动确认超时，但继续开始录音')
      showToast('流式识别启动可能有延迟，继续录音')
    } else {
      addDebugLog('success', '流式识别已确认启动')
    }

    // 开始录音
    addDebugLog('info', '开始录音...')
    await audioRecorder.value.startRecording()

    isRecording.value = true
    addDebugLog('success', `录音已开始，流式识别状态: ${isStreamingActive.value ? '已激活' : '未激活'}`)

  } catch (error: any) {
    addDebugLog('error', `开始录音失败: ${error.message}`)
    showToast(`开始录音失败: ${error.message}`)
    isRecording.value = false
  }
}

// 停止录音
const stopRecording = async () => {
  try {
    addDebugLog('info', '准备停止录音...')

    if (!audioRecorder.value || !isRecording.value) {
      addDebugLog('warn', '录音器未在录音状态')
      return
    }

    // 停止录音
    addDebugLog('info', '停止录音...')
    await audioRecorder.value.stopRecording()

    isRecording.value = false

    // 结束流式识别
    if (wsClient.value && wsClient.value.isConnected()) {
      addDebugLog('info', '结束流式识别...')
      wsClient.value.endStreamingRecognition()
    }

    addDebugLog('success', '录音已停止')

  } catch (error: any) {
    addDebugLog('error', `停止录音失败: ${error.message}`)
    showToast(`停止录音失败: ${error.message}`)
    isRecording.value = false
  }
}

// 录音计时器
const startRecordingTimer = () => {
  recordingDuration.value = 0
  recordingTimer.value = window.setInterval(() => {
    recordingDuration.value += 0.1
  }, 100)
}

const stopRecordingTimer = () => {
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
    recordingTimer.value = null
  }
}

// 滚动到消息底部
const scrollToMessagesBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 组件挂载
onMounted(async () => {
  addDebugLog('info', '组件已挂载')

  // 如果用户已登录，直接初始化组件
  if (userStore.userInfo) {
    await initializeComponents()
  }
})

// 组件卸载
onUnmounted(() => {
  addDebugLog('info', '组件即将卸载，清理资源...')

  // 停止录音
  if (isRecording.value && audioRecorder.value) {
    audioRecorder.value.cancelRecording()
  }

  // 清理计时器
  stopRecordingTimer()

  // 停止音频播放
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
    playingAudioId.value = null
  }

  // 断开WebSocket连接
  if (wsClient.value) {
    wsClient.value.disconnect()
  }

  addDebugLog('info', '资源清理完成')
})
</script>

<style scoped>
.streaming-voice-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 页面标题 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.page-header h2 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.status-indicators {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-badge.connected {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #4caf50;
}

.status-badge.disconnected {
  background: #ffebee;
  color: #c62828;
  border: 1px solid #f44336;
}

.status-badge.active {
  background: #e3f2fd;
  color: #1565c0;
  border: 1px solid #2196f3;
  animation: pulse-blue 2s infinite;
}

.status-badge.inactive {
  background: #f5f5f5;
  color: #757575;
  border: 1px solid #bdbdbd;
}

/* 登录区域 */
.login-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.login-card h3 {
  margin: 0 0 30px 0;
  color: #333;
  font-size: 20px;
}

.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 测试区域 */
.test-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 录音控制区域 */
.recording-control {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.recording-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.status-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  position: relative;
  background: #f5f5f5;
  border: 3px solid #ddd;
  transition: all 0.3s ease;
}

.status-circle.active {
  background: #ffebee;
  border-color: #f44336;
  color: #c62828;
}

.status-circle.streaming {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1565c0;
}

.pulse-animation {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px solid #f44336;
  border-radius: 50%;
  animation: pulse-red 1.5s infinite;
}

.status-text {
  text-align: left;
}

.primary-status {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.secondary-status {
  font-size: 14px;
  color: #666;
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.record-btn {
  padding: 15px 30px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.record-btn.start {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.record-btn.start:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
}

.record-btn.stop {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4);
}

.record-btn.stop:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.6);
}

.record-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 数据监控 */
.data-monitor {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.data-monitor h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.monitor-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  border: 1px solid #e9ecef;
}

.monitor-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.monitor-value {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}

/* 识别结果 */
.recognition-results {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.recognition-results h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.results-container {
  min-height: 100px;
}

.no-results {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px 0;
}

.partial-result, .final-result {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid #2196f3;
}

.final-result {
  border-left-color: #4caf50;
  background: #f1f8e9;
}

.result-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 600;
  text-transform: uppercase;
}

.result-text {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.result-confidence {
  font-size: 12px;
  color: #4caf50;
  font-weight: 600;
}

/* 消息历史 */
.message-history {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.message-history h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.messages-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 15px;
}

.no-messages {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px 0;
}

.message-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid #ddd;
}

.message-item.user {
  border-left-color: #2196f3;
  background: #e3f2fd;
}

.message-item.assistant {
  border-left-color: #4caf50;
  background: #f1f8e9;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-role {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
}

.message-time {
  font-size: 11px;
  color: #999;
}

.message-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.message-transcription {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.message-type {
  font-size: 11px;
  color: #2196f3;
  font-weight: 600;
  background: rgba(33, 150, 243, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 音频播放控件 */
.audio-player {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 10px 0;
  padding: 10px;
  background: rgba(33, 150, 243, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(33, 150, 243, 0.2);
}

.play-btn {
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.play-btn:hover:not(:disabled) {
  background: #1976d2;
  transform: translateY(-1px);
}

.play-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.audio-duration {
  font-size: 11px;
  color: #666;
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 调试面板 */
.debug-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.debug-panel.collapsed {
  margin-bottom: 0;
}

.debug-header {
  padding: 15px 20px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
  user-select: none;
}

.debug-header:hover {
  background: rgba(0, 0, 0, 0.02);
}

.debug-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.debug-toggle {
  font-size: 14px;
  color: #666;
  transition: transform 0.3s ease;
}

.debug-content {
  padding: 20px;
}

.debug-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.clear-btn {
  background: #f44336;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #d32f2f;
}

.auto-scroll-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
}

.debug-logs {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 10px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.debug-log-item {
  display: flex;
  gap: 10px;
  padding: 4px 0;
  font-size: 11px;
  line-height: 1.4;
  border-bottom: 1px solid #e9ecef;
}

.debug-log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

.log-level {
  min-width: 60px;
  flex-shrink: 0;
  font-weight: 600;
}

.debug-log-item.info .log-level {
  color: #2196f3;
}

.debug-log-item.success .log-level {
  color: #4caf50;
}

.debug-log-item.warn .log-level {
  color: #ff9800;
}

.debug-log-item.error .log-level {
  color: #f44336;
}

.log-message {
  flex: 1;
  color: #333;
  word-break: break-word;
}

.no-logs {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px 0;
}

/* 动画 */
@keyframes pulse-red {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

@keyframes pulse-blue {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 音频测试区域样式 */
.audio-test-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.audio-test-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.audio-test-section h4 {
  margin: 20px 0 15px 0;
  color: #555;
  font-size: 16px;
  font-weight: 500;
}

.audio-test-section h5 {
  margin: 15px 0 10px 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* 格式支持检测 */
.format-support {
  margin-bottom: 25px;
}

.support-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.support-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.format-name {
  font-weight: 500;
  color: #333;
}

.support-status.supported {
  color: #28a745;
  font-weight: 500;
}

.support-status.not-supported {
  color: #dc3545;
  font-weight: 500;
}

.support-level {
  font-size: 12px;
  color: #6c757d;
  margin-left: 5px;
}

/* 测试控件 */
.test-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.test-btn.record {
  background: #28a745;
  color: white;
}

.test-btn.record:hover:not(:disabled) {
  background: #218838;
}

.test-btn.stop {
  background: #dc3545;
  color: white;
}

.test-btn.stop:hover:not(:disabled) {
  background: #c82333;
}

.test-btn.play {
  background: #007bff;
  color: white;
}

.test-btn.play:hover:not(:disabled) {
  background: #0056b3;
}

.test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.recording-time {
  font-weight: 500;
  color: #dc3545;
  font-size: 14px;
}

/* 测试结果 */
.test-audio-result {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.audio-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #666;
}

.info-item .value {
  color: #333;
}

.test-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 测试样本 */
.sample-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.sample-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.sample-info {
  display: flex;
  gap: 15px;
  align-items: center;
  flex: 1;
}

.sample-name {
  font-weight: 500;
  color: #333;
}

.sample-type {
  font-size: 12px;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
}

.sample-format {
  font-size: 12px;
  color: #495057;
  background: #dee2e6;
  padding: 2px 6px;
  border-radius: 4px;
}

.sample-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.test-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.test-status.success {
  background: #d4edda;
  color: #155724;
}

.test-status.error {
  background: #f8d7da;
  color: #721c24;
}

.test-status.testing {
  background: #d1ecf1;
  color: #0c5460;
}

/* 播放监控 */
.monitor-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.audio-status {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-item .label {
  font-weight: 500;
  color: #666;
}

.status-item .value {
  color: #333;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  margin: 0 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6c757d;
  min-width: 50px;
  text-align: right;
}

.no-audio {
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .streaming-voice-test {
    padding: 15px;
  }

  .recording-status {
    flex-direction: column;
    gap: 15px;
  }

  .status-text {
    text-align: center;
  }

  .monitor-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .control-buttons {
    flex-direction: column;
    align-items: center;
  }

  .debug-controls {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
